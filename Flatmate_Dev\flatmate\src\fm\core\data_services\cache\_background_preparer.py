# fm/core/data_services/cache/_background_preparer.py
"""
Internal background worker for preparing data sets for caching.

This module contains the QRunnable worker that executes long-running
data preparation tasks in a background thread to avoid blocking the UI.
It is intended to be used internally by the DBIOService.
"""

from PySide6.QtCore import QObject, QRunnable, Signal

from fm.core.data_services.db_io_service import db_service
from fm.core.services.logger import log
from fm.logic.transaction_categorizer import TransactionCategorizer


class WorkerSignals(QObject):
    """
    Defines the signals available from a running worker thread.

    Supported signals are:
    - finished: No data, just indicates completion.
    - error: tuple (exctype, value, traceback.format_exc())
    - result: object data returned from processing, anything
    """
    finished = Signal(str)  # Key of the prepared data
    error = Signal(str, str)  # Key and error message
    result = Signal(str, object)  # Key and resulting dataframe


class BackgroundDataPreparer(QRunnable):
    """
    Background worker to prepare the transaction data for the Categorize view.
    """

    def __init__(self, key: str):
        super().__init__()
        self.key = key
        self.signals = WorkerSignals()

    def run(self):
        """
        Execute the data preparation task.
        """
        log.info(f"BackgroundDataPreparer started for key: '{self.key}'")
        try:
            if self.key == 'categorize':
                df = db_service.get_transactions_dataframe()
                if df is not None and not df.empty:
                    categorizer = TransactionCategorizer()
                    df = categorizer.add_category_to_df(df)
                    df = df.sort_values(by='date', ascending=False)
                self.signals.result.emit(self.key, df)
            else:
                raise ValueError(f"Unknown preparation key: {self.key}")

        except Exception as e:
            log.error(f"Error during background data preparation for key '{self.key}': {e}", exc_info=True)
            self.signals.error.emit(self.key, str(e))
        finally:
            self.signals.finished.emit(self.key)
            log.info(f"BackgroundDataPreparer finished for key: '{self.key}'")
