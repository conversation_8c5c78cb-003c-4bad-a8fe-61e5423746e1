# fm/core/data_services/prepared_views.py
"""
Registry for prepared table view builders.

This module defines the functions that build specific, pre-prepared DataFrames
for different application views. It also contains the central registry that maps
a view key (e.g., 'categorize') to its builder function.

This allows the background processing system to be completely generic.
"""

from typing import Callable, Dict
import pandas as pd

from fm.core.data_services.db_io_service import db_service
from fm.logic.transaction_categorizer import TransactionCategorizer
from fm.core.services.logger import log


def build_categorize_view() -> pd.DataFrame:
    """
    Builds the DataFrame needed for the 'Categorize' module view.

    - Fetches all transactions.
    - Applies categorization logic.
    - Sorts by date.
    """
    log.info("Building 'categorize' view data...")
    df = db_service.get_transactions_dataframe()
    if df is not None and not df.empty:
        categorizer = TransactionCategorizer()
        df = categorizer.add_category_to_df(df)
        df = df.sort_values(by='date', ascending=False)
        log.info("'categorize' view data built successfully.")
        return df
    log.warning("'categorize' view data is empty or None.")
    return pd.DataFrame()


# The central registry of prepared view builders
PREPARED_VIEW_REGISTRY: Dict[str, Callable[[], pd.DataFrame]] = {
    'categorize': build_categorize_view,
    # Add other view builders here in the future
}
