# Table View Flow Analysis

## Data Flow to Table View

```python
# From CategorizePresenter
self._original_df = df.copy()
self.view.set_dataframe(df)
```

```python
# In CatView
self.center_panel_manager.set_transactions(df)
```

```python
# In CenterPanelCoordinator
self.center_panel.set_transactions(df)
```

```python
# In TransactionViewPanel
@timing_decorator
def set_transactions(self, df: pd.DataFrame):
    # Configure table with sensible defaults
    self.transaction_table.configure(
        auto_size_columns=True,
        max_column_width=40,
        column_widths=standard_widths,
        editable_columns=editable_columns,
        show_toolbar=True,
        default_visible_columns=visible_columns
    ).set_dataframe(df).show()
```

## Performance Critical Points

1. **Data Configuration**
```python
# Multiple operations on DataFrame
self.transaction_table.configure(
    auto_size_columns=True,  # Requires column width calculations
    max_column_width=40,    # Column width constraints
    column_widths=standard_widths,  # Standard column widths
    editable_columns=editable_columns,  # Column permissions
    show_toolbar=True,      # UI component creation
    default_visible_columns=visible_columns  # Column visibility
).set_dataframe(df).show()  # Table rendering
```

2. **Column Management**
```python
# Column visibility and width management
visible_columns = []
for col in default_columns:
    if isinstance(col, Column):
        visible_columns.append(col.display_name)
    else:
        try:
            visible_columns.append(Columns.from_db_name(col).display_name)
        except:
            visible_columns.append(col)
```

3. **UI Rendering**
```python
# Multiple UI operations
self.transaction_table.configure(...).set_dataframe(df).show()
```

## Performance Considerations

1. **Memory Usage**
   - Multiple DataFrame copies
   - Column width calculations
   - UI component creation

2. **UI Operations**
   - Table rendering
   - Column configuration
   - Toolbar creation
   - Row height calculations

3. **Configuration Overhead**
   - Column visibility checks
   - Width calculations
   - Editable column permissions
   - Default column settings

## Timing Points

- `@timing_decorator` on `_init_ui` and `set_transactions`
- Column width calculations
- UI component creation
- Table rendering
- Signal connections
