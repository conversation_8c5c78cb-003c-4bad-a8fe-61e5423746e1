"""
Enhanced Database I/O Service - High-level API for database operations.

This module provides a unified interface for database operations including:
- Import/export functionality (CSV, DataFrame)
- Query helpers for common operations
- Statistics and reporting functions
- Column management integration

It serves as the primary entry point for all database interactions,
wrapping the lower-level repository classes and using the ColumnManager
for all column-related operations.
"""

from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Tuple

import pandas as pd

from fm.core.services.logger import log
from fm.core.database.sql_repository.sqlite_repository import SQLiteTransactionRepository
from fm.core.database.sql_repository.transaction_repository import ITransactionRepository, ImportResult, Transaction
from .standards.columns import Columns
from .cache.db_caching import DBCachingService

from PySide6.QtCore import QThreadPool


class DBIOService:
    """
    High-level service for database I/O operations.

    This class provides a unified API for all database operations,
    including import/export, queries, and statistics.
    """

    def __init__(
        self,
        repo: Optional[ITransactionRepository] = None,
    ):
        """
        Initialize the DB I/O Service.

        Args:
            repo: Optional repository instance for dependency injection.
                 If None, a new SQLiteTransactionRepository will be created.
        """
        self.repo = repo or SQLiteTransactionRepository()

        # Initialize caching service
        memory_limit = DBCachingService.get_recommended_memory_limit_mb()
        self._cache = DBCachingService(memory_limit_mb=memory_limit)
        log.info(f"DBIOService initialized with {memory_limit}MB cache limit")

        # Background preparation attributes
        self.thread_pool = QThreadPool.globalInstance()
        self._prepared_data: Dict[str, Optional[pd.DataFrame]] = {}
        self._preparation_status: Dict[str, str] = {}  # e.g., 'running', 'ready', 'error'

    # --- Import/export (df) --------------------------------------------------

    def update_database(
        self,
        df: pd.DataFrame,
        source_file: Optional[str] = None
    ) -> ImportResult:
        """
        Import transactions from a pandas DataFrame.

        Args:
            df: DataFrame containing transaction data
            source_file: Optional source file path for reference

        Returns:
            ImportResult with counts of added, duplicate, and error records
        """
        if df is None or df.empty:
            return ImportResult(
                added_count=0,
                duplicate_count=0,
                error_count=0,
                errors=["No data to import"]
            )

        try:
            df = df.copy()

            df_columns = set(df.columns)
            display_names = {c.display_name for c in Columns.get_all_columns()}
            if any(col in display_names for col in df_columns):
                df = self._convert_display_to_db_columns(df)

            balance_col = Columns.BALANCE.db_name
            source_uid_col = Columns.SOURCE_UID.db_name
            unique_id_col = Columns.UNIQUE_ID.db_name  # Legacy support

            # Check for balance OR any unique identifier (source_uid, unique_id)
            has_balance = balance_col in df.columns
            has_source_uid = source_uid_col in df.columns
            has_unique_id = unique_id_col in df.columns

            if not (has_balance or has_source_uid or has_unique_id):
                error_msg = "Import failed: DataFrame must contain either a 'balance', 'source_uid', or 'unique_id' column."
                log.error(error_msg)
                return ImportResult(added_count=0, duplicate_count=0, error_count=len(df), errors=[error_msg])

            date_col_db = Columns.DATE.db_name
            if date_col_db in df.columns and pd.api.types.is_datetime64_any_dtype(df[date_col_db]):
                df[date_col_db] = df[date_col_db].dt.strftime('%Y-%m-%d')

            if source_file:
                source_file_col = Columns.SOURCE_FILE.db_name
                df[source_file_col] = source_file

            return self.repo.add_transactions_from_df(df, source_file=source_file)
        except Exception as e:
            log.error(f"Critical error in DBIOService.update_database: {e}", exc_info=True)
            return ImportResult(added_count=0, duplicate_count=0, error_count=len(df), errors=[f"Error importing DataFrame: {str(e)}"])

    def get_transactions(
        self,
        only_columns_with_data: bool = True,
        use_display_names: bool = True,
        **filters
    ) -> pd.DataFrame:
        """
        Get transactions as a DataFrame with optional filtering and formatting.
        """
        df = self._fetch_raw_transactions_df(
            all_cols=not only_columns_with_data,
            **filters
        )

        if use_display_names:
            df = Columns.apply_display_names_to_df(df)
            if only_columns_with_data:
                display_cols_with_data = [c.display_name for c in Columns.get_display_columns() if c.db_name in df.columns and not df[c.db_name].isna().all()]
                return df[display_cols_with_data]

        return df

    def _fetch_raw_transactions_df(
            self,
            *, 
            all_cols: bool = False,
            only_columns_with_data: bool = True,
            **filters
        ) -> pd.DataFrame:
        """
        Internal method to fetch raw transactions as a DataFrame with database column names.
        """
        transactions = self.list_transactions(**filters)
        if not transactions:
            return pd.DataFrame()

        df = pd.DataFrame([t.to_dict() for t in transactions])

        if not all_cols:
            core_columns = [col.db_name for col in Columns.get_display_columns()]
            core_columns = [col for col in core_columns if col in df.columns]
            df = df[core_columns]

        if only_columns_with_data:
            cols_with_data = [col for col in df.columns if not df[col].isna().all()]
            df = df[cols_with_data]

        return df

    # --- Debugging and testing ------------------------------------

    def dump_raw_db_content(
        self,
        format: str = 'dataframe',
        file_path: Optional[Path] = None
    ) -> Union[pd.DataFrame, Dict, str, None]:
        """
        Dump raw database content for debugging and testing purposes.
        """
        transactions = self.list_transactions()
        if not transactions:
            return pd.DataFrame() if format == 'dataframe' else {} if format == 'json' else ''

        df = pd.DataFrame([t.to_dict() for t in transactions])

        if format == 'dataframe':
            result = df
        elif format == 'json':
            result = df.to_json(orient='records')
        elif format == 'csv':
            result = df.to_csv(index=False)
        else:
            raise ValueError(f"Unsupported format: {format}")

        if file_path:
            if format == 'json':
                df.to_json(file_path, orient='records')
            elif format == 'csv':
                df.to_csv(file_path, index=False)
            elif format == 'dataframe':
                df.to_pickle(file_path)
            return None

        return result

    def _convert_display_to_db_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Convert DataFrame columns from display names to database names.
        """
        display_to_db = {col.display_name: col.db_name for col in Columns.get_all_columns()}
        rename_cols = {col: display_to_db[col] for col in df.columns if col in display_to_db}

        if rename_cols:
            df = df.rename(columns=rename_cols)

        return df

    # --- Query helpers ---------------------------------------------

    def list_transactions(self, **filters) -> List[Transaction]:
        """
        List transactions with optional filtering.
        """
        return self.repo.get_transactions(filters)

    def get_transactions_dataframe(self, **filters) -> pd.DataFrame:
        """
        Get transactions as a pandas DataFrame with optional filtering.

        Uses cached data when available for instant results, falls back to database query.

        Args:
            **filters: Optional filters to apply (start_date, end_date, account_number, etc.)

        Returns:
            pandas DataFrame with transactions
        """
        return self._cache.get_transactions_dataframe(self, **filters)

    def initialize_cache(self, show_progress: bool = True) -> bool:
        """
        Initialize the database cache for faster data access.

        Should be called during application startup to load data into memory.

        Args:
            show_progress: Whether to show loading progress

        Returns:
            True if cache initialized successfully
        """
        return self._cache.initialize_cache(self, show_progress=show_progress)

    def get_cache_info(self) -> Dict[str, Any]:
        """
        Get information about the current cache state.

        Returns:
            Dictionary with cache statistics
        """
        return self._cache.get_cache_info()

    def get_database_stats(self) -> Dict[str, Any]:
        """
        Get cached database statistics for instant access.

        Returns:
            Dictionary with database statistics (total transactions, accounts, date ranges, etc.)
        """
        return self._cache.get_database_stats()

    # --- Background Data Preparation ----------------------------------------

    def start_background_preparation(self, key: str):
        """
        Start a background task to prepare a specific dataset.

        Args:
            key: A unique identifier for the dataset to prepare (e.g., 'categorize').
        """
        if self._preparation_status.get(key) == 'running':
            log.info(f"Preparation for key '{key}' is already running.")
            return

        # Look up the builder function from the registry
        if key not in PREPARED_VIEW_REGISTRY:
            log.error(f"No builder function found for key '{key}' in PREPARED_VIEW_REGISTRY")
            self._preparation_status[key] = 'error'
            return

        log.info(f"Starting background preparation for key: '{key}'")
        self._preparation_status[key] = 'running'

        # Get the builder function and create the generic worker
        builder_function = PREPARED_VIEW_REGISTRY[key]
        worker = TableViewBuilder(key=key, builder_function=builder_function)

        # Connect signals to slots
        worker.signals.result.connect(self._on_preparation_result)
        worker.signals.error.connect(self._on_preparation_error)
        worker.signals.finished.connect(self._on_preparation_finished)

        self.thread_pool.start(worker)

    def is_preparation_complete(self, key: str) -> bool:
        """
        Check if the background data preparation for a given key is complete.

        Returns:
            True if the data is ready or an error occurred, False if still running.
        """
        status = self._preparation_status.get(key)
        return status == 'ready' or status == 'error'

    def get_prepared_dataframe(self, key: str) -> Optional[pd.DataFrame]:
        """
        Get the prepared dataframe for a given key.

        Returns:
            The prepared DataFrame if ready, otherwise None.
        """
        if self.is_preparation_complete(key):
            return self._prepared_data.get(key)
        return None

    def _on_preparation_result(self, key: str, data: pd.DataFrame):
        """
        Slot to handle the result from the background worker.
        """
        log.info(f"Successfully prepared data for key: '{key}'")
        self._prepared_data[key] = data

    def _on_preparation_error(self, key: str, error_message: str):
        """
        Slot to handle an error from the background worker.
        """
        log.error(f"Failed to prepare data for key '{key}': {error_message}")
        self._prepared_data[key] = None
        self._preparation_status[key] = 'error'

    def _on_preparation_finished(self, key: str):
        """
        Slot to handle the completion of the background worker.
        """
        # If status is still 'running', it means no error occurred.
        if self._preparation_status.get(key) == 'running':
            self._preparation_status[key] = 'ready'
        log.info(f"Finished background preparation task for key: '{key}'")


    def get_transaction_by_id(self, transaction_id: int) -> Optional[Transaction]:
        """
        Get a transaction by its ID.
        """
        return self.repo.get_transaction_by_id(transaction_id)

    def update_transaction(self, transaction_id: int, updates: Dict[str, Any]) -> bool:
        """
        Update a transaction.
        """
        db_updates = {}
        for key, value in updates.items():
            col = Columns.from_display_name(key)
            if col:
                db_updates[col.db_name] = value
            else:
                db_updates[key] = value

        return self.repo.update_transaction(transaction_id, db_updates)

    def update_transaction_tags(self, transaction_id: int, tags: str) -> bool:
        """
        Update the tags for a transaction.
        """
        return self.update_transaction(transaction_id, {Columns.TAGS.db_name: tags})

    def get_unique_account_numbers(self) -> List[str]:
        """
        Get a list of unique account numbers, using cache when available.
        """
        return self._cache.get_unique_account_numbers(self)

    # --- Statistics and reporting ---------------------------------

    def get_db_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the transaction database.
        """
        transactions = self.list_transactions()
        if not transactions:
            return {
                'total_count': 0,
                'date_range': (None, None),
                'account_counts': {},
                'total_amount': 0.0
            }

        date_col = Columns.DATE.db_name
        account_col = Columns.ACCOUNT.db_name
        amount_col = Columns.AMOUNT.db_name

        dates = [getattr(t, date_col) for t in transactions if hasattr(t, date_col) and getattr(t, date_col)]
        min_date = min(dates) if dates else None
        max_date = max(dates) if dates else None

        account_counts = {}
        for t in transactions:
            account = getattr(t, account_col, None)
            if account:
                account_counts[account] = account_counts.get(account, 0) + 1

        total_amount = sum(getattr(t, amount_col, 0) for t in transactions)

        return {
            'total_count': len(transactions),
            'date_range': (min_date, max_date),
            'account_counts': account_counts,
            'total_amount': total_amount
        }

    def delete_all_transactions(self) -> int:
        """
        Delete all transactions from the database.
        """
        return self.repo.delete_all_transactions()

    @staticmethod
    def convert_transactions_to_dataframe(transactions: List[Transaction], ensure_columns: Optional[List[str]] = None) -> pd.DataFrame:
        """
        Convert a list of Transaction objects to a pandas DataFrame.

        Args:
            transactions: List of Transaction objects.
            ensure_columns: Optional list of database column names to ensure exist in the DataFrame.

        Returns:
            A pandas DataFrame with db_names as columns.
        """
        if not transactions:
            return pd.DataFrame(columns=ensure_columns) if ensure_columns else pd.DataFrame()

        try:
            data = [tx.to_dict() for tx in transactions]
            df = pd.DataFrame(data)

            if ensure_columns:
                for col in ensure_columns:
                    if col not in df.columns:
                        df[col] = None
            return df
        except Exception as e:
            log.error(f"Error converting transactions to DataFrame: {str(e)}", exc_info=True)
            raise