# fm/core/data_services/cache/_background_preparer.py
"""
Internal background worker for preparing data sets for caching.

This module contains the QRunnable worker that executes long-running
data preparation tasks in a background thread to avoid blocking the UI.
It is intended to be used internally by the DBIOService.
"""

from PySide6.QtCore import QObject, QRunnable, Signal

from typing import Callable
import pandas as pd

from fm.core.services.logger import log


class WorkerSignals(QObject):
    """
    Defines the signals available from a running worker thread.

    Supported signals are:
    - finished: No data, just indicates completion.
    - error: tuple (exctype, value, traceback.format_exc())
    - result: object data returned from processing, anything
    """
    finished = Signal(str)  # Key of the prepared data
    error = Signal(str, str)  # Key and error message
    result = Signal(str, object)  # Key and resulting dataframe


class TableViewBuilder(QRunnable):
    """
    Generic background worker that executes a provided function to build a DataFrame.
    """

    def __init__(self, key: str, builder_function: Callable[[], pd.DataFrame]):
        super().__init__()
        self.key = key
        self.builder = builder_function
        self.signals = WorkerSignals()

    def run(self):
        """
        Execute the provided builder function and emit the result.
        """
        log.info(f"TableViewBuilder started for key: '{self.key}'")
        try:
            result_df = self.builder()
            self.signals.result.emit(self.key, result_df)

        except Exception as e:
            log.error(f"Error in TableViewBuilder for key '{self.key}': {e}", exc_info=True)
            self.signals.error.emit(self.key, str(e))
        finally:
            self.signals.finished.emit(self.key)
            log.info(f"TableViewBuilder finished for key: '{self.key}'")
