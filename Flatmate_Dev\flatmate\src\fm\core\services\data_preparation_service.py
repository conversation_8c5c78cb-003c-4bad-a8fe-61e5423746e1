"""
Service for preparing data in the background to improve UI responsiveness.
"""
from __future__ import annotations

import pandas as pd
from PySide6.QtCore import QObject, Signal, QRunnable, QThreadPool

from .logger import log
from ..data_services import DBIOService
from ...modules.categorize.core.categorizer import TransactionCategorizer


class DataPreparationWorker(QRunnable):
    """Worker to prepare data in a background thread."""

    def __init__(self, data_service: DBIOService, categorizer: TransactionCategorizer):
        super().__init__()
        self.data_service = data_service
        self.categorizer = categorizer
        self.signals = self.WorkerSignals()

    class WorkerSignals(QObject):
        finished = Signal(object)  # Emits the prepared DataFrame
        error = Signal(str)

    def run(self):
        """The main work of the thread."""
        try:
            log.info("Background Worker: Starting data preparation for Categorize module.")
            # 1. Fetch raw data from the cache
            df = self.data_service.get_transactions_dataframe()
            if df.empty:
                log.warning("Background Worker: No transactions found in cache. Nothing to prepare.")
                self.signals.finished.emit(df)
                return

            # 2. Apply categorization
            log.info(f"Background Worker: Applying categorization to {len(df)} transactions...")
            df["category"] = df.apply(self.categorizer.categorize_row, axis=1)

            # 3. Apply default sorting (example, can be made more robust)
            if 'date' in df.columns:
                df = df.sort_values(by='date', ascending=False)

            log.info("Background Worker: Data preparation complete.")
            self.signals.finished.emit(df)

        except Exception as e:
            log.error(f"Background Worker: Error during data preparation: {e}")
            self.signals.error.emit(str(e))


class DataPreparationService:
    """Singleton service to manage pre-loading and preparation of data."""
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DataPreparationService, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return
        self.thread_pool = QThreadPool.globalInstance()
        self.categorize_df: pd.DataFrame | None = None
        self.is_ready = False
        self.data_service = DBIOService()
        self.categorizer = TransactionCategorizer()
        self._initialized = True

    def start_preparation(self):
        """Starts the background data preparation for all necessary modules."""
        log.info("DataPreparationService: Kicking off background data preparation.")
        worker = DataPreparationWorker(self.data_service, self.categorizer)
        worker.signals.finished.connect(self._on_preparation_finished)
        worker.signals.error.connect(self._on_preparation_error)
        self.thread_pool.start(worker)

    def _on_preparation_finished(self, df: pd.DataFrame):
        """Callback when the worker has finished."""
        log.info("DataPreparationService: Successfully received prepared data.")
        self.categorize_df = df
        self.is_ready = True

    def _on_preparation_error(self, error_message: str):
        """Callback when the worker encounters an error."""
        log.error(f"DataPreparationService: Failed to prepare data: {error_message}")
        self.categorize_df = None
        self.is_ready = True  # Set to True to unblock UI; presenter handles None data

    def get_categorize_dataframe(self) -> pd.DataFrame | None:
        """Returns the prepared DataFrame for the Categorize module."""
        return self.categorize_df


# Create a singleton instance
data_preparation_service = DataPreparationService()
