2025-07-16 01:28:07 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-16 01:28:08 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-16 01:28:08 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-16 01:28:08 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-16 01:28:08 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-16 01:28:09 - [fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-16 01:28:09 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 18 user preferences
2025-07-16 01:28:09 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-16 01:28:09 - [fm.main] [INFO] - Application starting...
2025-07-16 01:28:11 - [fm.main] [INFO] - 
=== Setting up Module Coordinator ===
2025-07-16 01:28:11 - [fm.module_coordinator] [INFO] - Initializing Module Coordinator
2025-07-16 01:28:11 - [fm.module_coordinator] [DEBUG] - Loaded recent modules: ['home', 'categorize', 'update_data']
2025-07-16 01:28:11 - [fm.module_coordinator] [INFO] - Initializing Module Factories
2025-07-16 01:28:11 - [fm.module_coordinator] [DEBUG] - Registered module factories: ['home', 'update_data', 'categorize']
2025-07-16 01:28:11 - [fm.main] [INFO] - 
=== Initializing Database Cache ===
2025-07-16 01:28:11 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-16 01:28:11 - [fm.core.data_services.db_io_service] [DEBUG] - DBIOService initialized with 377MB cache limit
2025-07-16 01:28:11 - [fm.core.data_services.cache.db_caching] [INFO] - Initializing database cache...
2025-07-16 01:28:11 - [fm.core.data_services.cache.db_caching] [INFO] - Loading all transactions from database with all columns...
2025-07-16 01:28:12 - [fm.core.data_services.cache.db_caching] [DEBUG] - Cached database statistics: 2099 transactions, 3 accounts
2025-07-16 01:28:12 - [fm.core.data_services.cache.db_caching] [INFO] - Database cache initialized: 2099 transactions, 2.3MB memory, 0.7s
2025-07-16 01:28:12 - [fm.main] [INFO] - Database cache initialized: 2099 transactions, 2.3MB memory usage
2025-07-16 01:28:12 - [fm.module_coordinator] [INFO] - Starting Application
2025-07-16 01:28:12 - [fm.modules.home.home_presenter] [INFO] - Initializing Home Presenter
2025-07-16 01:28:12 - [fm.modules.home.home_presenter] [DEBUG] - Home Presenter initialization complete
2025-07-16 01:28:12 - [fm.modules.home.home_presenter] [INFO] - Initializing Home Module
2025-07-16 01:28:12 - [fm.modules.home.home_presenter] [DEBUG] - Connecting Home View signals
2025-07-16 01:28:12 - [fm.modules.home.home_presenter] [DEBUG] - Home Module initialization complete
2025-07-16 01:28:12 - [fm.main] [INFO] - 
=== Application Ready ===
2025-07-16 01:28:17 - [fm.modules.home.home_presenter] [DEBUG] - Home Module cleanup complete
2025-07-16 01:28:17 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-16 01:28:17 - [fm.core.data_services.db_io_service] [DEBUG] - DBIOService initialized with 372MB cache limit
2025-07-16 01:28:17 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Initializing TransactionViewPanel
2025-07-16 01:28:17 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: TransactionViewPanel._init_ui
2025-07-16 01:28:17 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Setting up TransactionViewPanel UI
2025-07-16 01:28:17 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Creating CustomTableView_v2 for transactions
2025-07-16 01:28:17 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel UI setup complete
2025-07-16 01:28:17 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: TransactionViewPanel._init_ui took 0.040s
2025-07-16 01:28:17 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Connecting TransactionViewPanel signals
2025-07-16 01:28:17 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel signals connected
2025-07-16 01:28:17 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-16 01:28:17 - [fm.core.data_services.db_io_service] [DEBUG] - DBIOService initialized with 372MB cache limit
2025-07-16 01:28:17 - [fm.modules.base.base_module_view] [INFO] - Setting up CatView in Main Window
2025-07-16 01:28:17 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Left Panel
2025-07-16 01:28:17 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Center Panel
2025-07-16 01:28:17 - [fm.modules.base.base_module_view] [INFO] - CatView setup complete
2025-07-16 01:28:17 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.database.last_account = None (Source: cat_presenter.py:initialize)
2025-07-16 01:28:17 - [fm.modules.categorize.cat_presenter] [INFO] - Auto-loading ALL transactions from database...
2025-07-16 01:28:17 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: CategorizePresenter._handle_load_db
2025-07-16 01:28:17 - [fm.modules.categorize.cat_presenter] [INFO] - Handling request to load data into Categorize view.
2025-07-16 01:28:17 - [fm.core.utils.timing_decorator] [ERROR] - ⏱️  TIMING ERROR: CategorizePresenter._handle_load_db failed after 0.001s: 'DBIOService' object has no attribute 'is_preparation_complete'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\core\utils\timing_decorator.py", line 36, in wrapper
    result = func(*args, **kwargs)
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\categorize\cat_presenter.py", line 276, in _handle_load_db
    if not self.data_service.is_preparation_complete('categorize'):
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'DBIOService' object has no attribute 'is_preparation_complete'
