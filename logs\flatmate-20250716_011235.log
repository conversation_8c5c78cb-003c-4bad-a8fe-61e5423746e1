2025-07-16 01:12:35 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-16 01:12:35 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-16 01:12:35 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-16 01:12:35 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-16 01:12:35 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-16 01:12:35 - [fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-16 01:12:35 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 18 user preferences
2025-07-16 01:12:35 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-16 01:12:35 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-16 01:12:35 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-16 01:12:35 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-16 01:12:36 - [src.fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-16 01:12:36 - [main] [INFO] - Application starting...
2025-07-16 01:12:42 - [main] [INFO] - 
=== Setting up Module Coordinator ===
2025-07-16 01:12:42 - [src.fm.module_coordinator] [INFO] - Initializing Module Coordinator
2025-07-16 01:12:42 - [src.fm.module_coordinator] [DEBUG] - Loaded recent modules: ['home', 'categorize', 'update_data']
2025-07-16 01:12:42 - [src.fm.module_coordinator] [INFO] - Initializing Module Factories
2025-07-16 01:12:42 - [src.fm.module_coordinator] [DEBUG] - Registered module factories: ['home', 'update_data', 'categorize']
2025-07-16 01:12:43 - [main] [INFO] - 
=== Initializing Database Cache ===
2025-07-16 01:12:43 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-16 01:12:43 - [src.fm.core.data_services.db_io_service] [DEBUG] - DBIOService initialized with 115MB cache limit
2025-07-16 01:12:43 - [src.fm.core.data_services.cache.db_caching] [INFO] - Initializing database cache...
2025-07-16 01:12:43 - [src.fm.core.data_services.cache.db_caching] [INFO] - Loading all transactions from database with all columns...
2025-07-16 01:12:45 - [src.fm.core.data_services.cache.db_caching] [DEBUG] - Cached database statistics: 2099 transactions, 3 accounts
2025-07-16 01:12:45 - [src.fm.core.data_services.cache.db_caching] [INFO] - Database cache initialized: 2099 transactions, 2.3MB memory, 2.1s
2025-07-16 01:12:45 - [main] [INFO] - Database cache initialized: 2099 transactions, 2.3MB memory usage
2025-07-16 01:12:45 - [src.fm.module_coordinator] [INFO] - Starting Application
2025-07-16 01:12:46 - [src.fm.modules.home.home_presenter] [INFO] - Initializing Home Presenter
2025-07-16 01:12:46 - [src.fm.modules.home.home_presenter] [DEBUG] - Home Presenter initialization complete
2025-07-16 01:12:46 - [src.fm.modules.home.home_presenter] [INFO] - Initializing Home Module
2025-07-16 01:12:46 - [src.fm.modules.home.home_presenter] [DEBUG] - Connecting Home View signals
2025-07-16 01:12:46 - [src.fm.modules.home.home_presenter] [DEBUG] - Home Module initialization complete
2025-07-16 01:12:46 - [main] [INFO] - 
=== Application Ready ===
